# Design Document: S3 Media Upload System

## Overview

This document outlines the design for implementing a media upload and management system using Amazon S3 signed URLs. The system will allow users to upload media files directly to S3 storage from the browser, bypassing the application server for the actual file transfer. This approach offers several advantages:

1. **Improved Performance**: Files are uploaded directly to S3, reducing server load
2. **Scalability**: The system can handle many concurrent uploads without server bottlenecks
3. **Cost Efficiency**: Reduced server bandwidth usage and processing requirements
4. **Reliability**: Leverages AWS S3's high availability and durability

The implementation will extend the existing upload functionality while maintaining compatibility with the current UI components and user experience.

## Architecture

### High-Level Architecture

```mermaid
graph TD
    Client[Client Browser] -->|1. Request signed URL| Server[Backend Server]
    Server -->|2. Generate signed URL| S3[AWS S3]
    Server -->|3. Return signed URL| Client
    Client -->|4. Upload file directly| S3
    S3 -->|5. Upload confirmation| Client
    Client -->|6. Notify upload complete| Server
    Server -->|7. Update metadata DB| DB[(Database)]
    Server -->|8. Generate thumbnails| S3
```

### Components and Interfaces

#### Frontend Components

1. **S3UploadService**: A new service that handles S3 signed URL requests and direct uploads

   - `getSignedUrl(fileName, fileType, fileSize)`: Requests a signed URL from the backend
   - `uploadToS3(file, signedUrl, onProgress)`: Uploads a file directly to S3 using the signed URL
   - `notifyUploadComplete(fileKey, metadata)`: Notifies the backend that an upload is complete

2. **MediaLibraryManager**: A service to manage uploaded media files

   - `listMedia(page, filters)`: Lists uploaded media with pagination and filtering
   - `getMediaDetails(mediaId)`: Gets detailed information about a specific media file
   - `deleteMedia(mediaId)`: Deletes a media file from S3

3. **Enhanced UnifiedFileUpload Component**: Extend the existing component to support S3 uploads
   - Add support for direct S3 uploads while maintaining the current API
   - Add support for resumable uploads for large files

#### Backend Components

1. **S3Controller**: A new controller for S3-related operations

   - `generateSignedUrl`: Generates and returns a signed URL for uploading
   - `completeUpload`: Processes post-upload tasks like thumbnail generation
   - `listMedia`: Returns a list of media files for the authenticated user
   - `deleteMedia`: Removes a media file from S3

2. **S3Service**: A service layer for S3 operations

   - `generateSignedUrl(fileName, fileType, fileSize, userId)`: Creates a signed URL
   - `generateThumbnail(fileKey)`: Generates thumbnails for images and videos
   - `deleteFile(fileKey)`: Deletes a file from S3
   - `listUserFiles(userId, filters)`: Lists files for a specific user

3. **MediaRepository**: A data layer for storing and retrieving media metadata
   - `saveMediaMetadata(metadata)`: Saves metadata about uploaded media
   - `getMediaByUser(userId, page, filters)`: Retrieves media metadata with pagination
   - `deleteMedia(mediaId)`: Removes media metadata

### Data Models

#### Media Metadata

```typescript
interface MediaMetadata {
  id: string; // Unique identifier
  userId: string; // Owner of the file
  fileName: string; // Original file name
  fileKey: string; // S3 object key
  fileType: string; // MIME type
  fileSize: number; // Size in bytes
  uploadDate: Date; // When the file was uploaded
  url: string; // Public URL (if applicable)
  thumbnailUrl?: string; // URL to thumbnail
  width?: number; // For images and videos
  height?: number; // For images and videos
  duration?: number; // For audio and video
  status: "processing" | "ready" | "error"; // Processing status
}
```

#### Signed URL Request

```typescript
interface SignedUrlRequest {
  fileName: string; // Original file name
  fileType: string; // MIME type
  fileSize: number; // Size in bytes
}
```

#### Signed URL Response

```typescript
interface SignedUrlResponse {
  url: string; // The signed URL
  fileKey: string; // The S3 object key to use
  fields?: {
    // Additional fields for POST uploads
    [key: string]: string;
  };
  expiresAt: number; // Expiration timestamp
}
```

### S3 Storage Structure

The S3 bucket will be organized with the following structure:

```
bucket-name/
├── uploads/
│   ├── user-{userId}/
│   │   ├── images/
│   │   │   └── {fileKey}.{extension}
│   │   ├── videos/
│   │   │   └── {fileKey}.{extension}
│   │   └── audio/
│   │       └── {fileKey}.{extension}
├── thumbnails/
│   └── {fileKey}-{size}.{extension}
└── temp/
    └── {uploadId}/
        └── {partNumber}
```

### Authentication and Security

1. **User Authentication**: All requests to generate signed URLs require an authenticated user
2. **URL Expiration**: Signed URLs will expire after a short period (15 minutes)
3. **Content Type Restriction**: Signed URLs will be restricted to specific content types
4. **File Size Limits**: Maximum file size limits will be enforced
5. **CORS Configuration**: The S3 bucket will have CORS configured to allow uploads from the application domain

## Error Handling

### Frontend Error Handling

1. **Network Errors**: Detect and handle network interruptions during upload
2. **Expired URLs**: Handle expired signed URLs by requesting a new one
3. **S3 Errors**: Parse and display S3 error responses in a user-friendly way
4. **Validation Errors**: Validate files before requesting signed URLs

### Backend Error Handling

1. **Authentication Errors**: Return appropriate 401/403 responses
2. **Validation Errors**: Validate request parameters and return 400 responses
3. **S3 Service Errors**: Handle and log S3 SDK errors
4. **Database Errors**: Handle and log database errors

## Testing Strategy

### Unit Tests

1. **Frontend Services**: Test S3UploadService and MediaLibraryManager methods
2. **Backend Services**: Test S3Service and MediaRepository methods
3. **Controllers**: Test S3Controller endpoints

### Integration Tests

1. **End-to-End Upload Flow**: Test the complete upload process
2. **Media Management**: Test listing, retrieving, and deleting media
3. **Error Scenarios**: Test handling of various error conditions

### Performance Tests

1. **Concurrent Uploads**: Test multiple simultaneous uploads
2. **Large File Uploads**: Test uploading large files
3. **Pagination**: Test listing large numbers of media files

## Implementation Considerations

### AWS SDK Configuration

The backend will use the AWS SDK for JavaScript/TypeScript to interact with S3. Configuration will include:

1. **Region**: Set to the appropriate AWS region
2. **Credentials**: Use IAM role or environment variables for credentials
3. **Bucket Policy**: Configure appropriate bucket policies
4. **CORS Configuration**: Set up CORS to allow uploads from the application domain

### Resumable Uploads

For large files, we'll implement resumable uploads using S3's multipart upload API:

1. **Initiate Multipart Upload**: Start a multipart upload and get an upload ID
2. **Upload Parts**: Upload file chunks as separate parts
3. **Complete Upload**: Finalize the upload by providing the ETag for each part
4. **Abort Upload**: Clean up incomplete uploads if necessary

### Thumbnail Generation

For images and videos, we'll generate thumbnails after upload:

1. **Images**: Resize using a serverless function triggered by S3 events
2. **Videos**: Extract a frame using FFmpeg in a serverless function

## Migration Strategy

To ensure a smooth transition from the current upload system to the S3-based system:

1. **Parallel Implementation**: Implement the new system alongside the existing one
2. **Feature Flag**: Use a feature flag to control which system is used
3. **Gradual Rollout**: Start with a small percentage of users and gradually increase
4. **Monitoring**: Monitor performance and error rates during the transition
5. **Rollback Plan**: Have a plan to revert to the old system if issues arise
