# Product Overview

Fabric Video Editor is a comprehensive web-based video editing application that provides a canvas-based editing experience. The application allows users to create, edit, and export video compositions by combining various media elements including videos, images, audio, and text.

## Core Features

- **Canvas-based editing** with intuitive drag-and-drop controls using Fabric.js
- **Timeline management** for element duration and animation control
- **Multi-media support** for videos, images, audio, GIFs, and text elements
- **Animation system** with customizable effects and transitions
- **Real-time preview** with synchronized playback
- **Video export** using FFmpeg backend processing
- **Project management** with save/load functionality

## Architecture

The application follows a client-server architecture:

- **Frontend**: React-based editor with MobX state management
- **Backend**: Node.js server with FFmpeg integration for video processing
- **Canvas Engine**: Fabric.js for interactive element manipulation
- **Animation Engine**: Anime.js for smooth transitions and effects

## Target Use Cases

- Social media content creation
- Educational video production
- Marketing and promotional videos
- Personal video projects
- Template-based video generation

The application is designed for both casual users and content creators who need a powerful yet accessible video editing solution that runs entirely in the browser.
