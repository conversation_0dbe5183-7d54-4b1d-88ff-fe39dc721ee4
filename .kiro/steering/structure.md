# Project Structure

## Root Directory Organization

```
fabric-video-editor/
├── frontend/           # React frontend application
├── server/            # Node.js backend server
├── docs/              # Project documentation
├── .cursor/           # Cursor IDE configuration
├── .kiro/             # Kiro AI assistant configuration
└── public/            # Shared public assets
```

## Frontend Structure (`frontend/`)

### Core Directories

- **`src/components/`** - Reusable UI components

  - `common/` - Shared components (ColorPicker, FileUpload, etc.)
  - `Dashboard.tsx` - Main dashboard component

- **`src/editor/`** - Video editor core functionality

  - `components/` - Editor-specific components (navbar, drag, color pickers)
  - `control-item/` - Property panels for different element types
  - `menu-item/` - Left sidebar menu items (Images, Videos, Text, etc.)
  - `timeline/` - Timeline components and drag functionality
  - `entity/` - Resource management components

- **`src/store/`** - MobX state management

  - `Store.ts` - Main application store
  - `ElementManager.ts` - Canvas element operations
  - `AnimationManager.ts` - Animation control
  - `CanvasManager.ts` - Canvas state management
  - `TimelineManager.ts` - Timeline operations
  - Individual managers for specific features

- **`src/utils/`** - Utility functions
  - `fabric-utils.ts` - Fabric.js helper functions
  - `ShapeFactory.ts` - Shape creation utilities
  - `timeUtils.ts` - Time calculation helpers

### Key Files

- `App.tsx` - Main application component
- `types.ts` - Global TypeScript type definitions
- `index.tsx` - Application entry point

## Backend Structure (`server/`)

### Core Directories

- **`src/ffmpeg/`** - FFmpeg command generation

  - `core/` - Core processing classes
    - `filters/` - Filter generators for different media types
    - `generators/` - Content generators (shapes, etc.)
  - `utils/` - FFmpeg utility functions
  - `FFmpegCommandGenerator.ts` - Main command builder

- **`src/controllers/`** - API route handlers

  - `VideoController.ts` - Video processing endpoints
  - `TemplateController.ts` - Template management
  - `AiController.ts` - AI integration

- **`src/services/`** - Business logic services
  - `ProgressTracker.ts` - Video generation progress tracking
  - `TaskQueue.ts` - Background task management

### Key Files

- `index.ts` - Server entry point and Express setup
- `types.ts` - Backend type definitions

## Naming Conventions

### Files and Directories

- **Components**: PascalCase (e.g., `VideoPlayer.tsx`)
- **Utilities**: camelCase (e.g., `timeUtils.ts`)
- **Stores/Managers**: PascalCase with descriptive suffix (e.g., `ElementManager.ts`)
- **Types**: camelCase for files, PascalCase for interfaces

### Code Conventions

- **React Components**: PascalCase function components
- **Hooks**: camelCase starting with `use` (e.g., `useElementDrag`)
- **Store Methods**: camelCase with descriptive verbs
- **Constants**: UPPER_SNAKE_CASE in dedicated constants files

## Import Organization

### Frontend Imports Order

1. React and external libraries
2. Internal components and utilities
3. Store imports
4. Type imports
5. Relative imports

### Backend Imports Order

1. Node.js built-ins
2. External packages
3. Internal modules
4. Type imports

## State Management Architecture

### MobX Store Pattern

- **Main Store** (`Store.ts`) - Central state container
- **Manager Classes** - Feature-specific state management
- **Observable Properties** - Reactive state updates
- **Actions** - State modification methods

### Manager Responsibilities

- `ElementManager` - Canvas element CRUD operations
- `AnimationManager` - Animation timeline control
- `CanvasManager` - Canvas rendering and interaction
- `TimelineManager` - Timeline state and playback
- `HistoryManager` - Undo/redo functionality

## Component Architecture

### Editor Components

- **Container Components** - Handle state and business logic
- **Presentation Components** - Pure UI rendering
- **Control Components** - Property editing panels
- **Menu Components** - Resource browsers and tools

### Timeline Components

- **Track-based Structure** - Separate tracks for different media types
- **Drag and Drop** - Element positioning and duration control
- **Context Menus** - Right-click operations
- **Progress Indicators** - Visual feedback for operations
