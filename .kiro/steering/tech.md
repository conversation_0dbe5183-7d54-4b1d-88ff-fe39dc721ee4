# Technology Stack

## Frontend Stack

- **Framework**: React 18.3.1 with TypeScript 5.7.3
- **State Management**: MobX 6.13.6 for reactive state management
- **Canvas Library**: Fabric.js 5.3.1 for interactive canvas manipulation
- **Animation**: Anime.js 3.2.2 for smooth animations and transitions
- **UI Components**: Material-UI (MUI) 6.4.3 with Emotion styling
- **Build Tool**: Create React App with React Scripts 5.0.1
- **Drag & Drop**: @dnd-kit for timeline and element management
- **Audio Processing**: Wavesurfer.js 7.9.9 for waveform visualization

## Backend Stack

- **Runtime**: Node.js with TypeScript 5.8.3
- **Framework**: Express.js 4.21.2
- **Video Processing**: FFmpeg (external dependency)
- **File Upload**: Multer 1.4.5 for media handling
- **Image Processing**: ImageMagick 0.1.3
- **Logging**: Winston 3.17.0
- **Security**: Helmet 8.1.0, CORS 2.8.5, Rate Limiting

## Development Tools

- **Package Manager**: npm
- **Linting**: ESLint with TypeScript support
- **Testing**: Jest 29.7.0 with ts-jest
- **Development Server**: ts-node-dev for hot reloading
- **Type Checking**: Strict TypeScript configuration

## External Dependencies

- **FFmpeg**: Required for video processing (must be installed on server)
- **Third-party APIs**: Jamendo API for music library integration

## Common Commands

### Frontend Development

```bash
cd frontend
npm install          # Install dependencies
npm start           # Start development server (port 3000)
npm run build       # Build for production
npm test            # Run tests
```

### Backend Development

```bash
cd server
npm install          # Install dependencies
npm run dev         # Start development server with hot reload
npm run build       # Compile TypeScript to JavaScript
npm start           # Start production server
npm run setup-fonts # Setup system fonts for text rendering
npm test            # Run tests
```

### Full Stack Development

```bash
# Terminal 1 - Backend
cd server && npm run dev

# Terminal 2 - Frontend
cd frontend && npm start
```

## Build Configuration

- **Frontend**: Uses Create React App configuration with custom browser polyfills
- **Backend**: TypeScript compilation to `dist/` directory with ES2020 target
- **Environment**: Supports `.env` files for configuration
- **CORS**: Configured for cross-origin requests between frontend and backend
