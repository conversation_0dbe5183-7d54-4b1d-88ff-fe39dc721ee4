# 中文字体配置指南

本指南将帮助您解决在线视频编辑器中中文文字显示乱码的问题。

## 问题描述

当前端添加中文文字元素时，后端FFmpeg生成的视频中可能出现乱码，主要原因包括：

1. **字体文件缺失**：系统缺少支持中文的字体文件
2. **字体路径配置错误**：字体目录未正确设置
3. **字体文件命名不匹配**：期望的字体文件名与实际不符
4. **文本编码问题**：FFmpeg处理中文文本时的编码转换问题

## 解决方案

### 1. 自动检查和设置

运行字体设置脚本来检查当前系统的字体配置：

```bash
cd server
npm run setup-fonts
```

这个脚本会：
- 检查系统中可用的中文字体
- 验证自定义字体目录
- 生成字体配置报告
- 提供安装建议

### 2. 手动字体安装

#### macOS 系统

macOS 通常已预装支持中文的字体：

```bash
# 检查系统字体
ls /System/Library/Fonts/PingFang*
ls /System/Library/Fonts/STHeiti*
```

如需安装额外字体：
1. 下载字体文件（.ttf 或 .ttc 格式）
2. 双击字体文件安装，或复制到 `/Library/Fonts/` 目录

#### Windows 系统

Windows 系统通常包含以下中文字体：
- 黑体 (SimHei.ttf)
- 宋体 (SimSun.ttc)
- 微软雅黑 (msyh.ttc)

检查字体：
```cmd
dir C:\Windows\Fonts\*sim*
dir C:\Windows\Fonts\*msy*
```

如果缺少中文字体：
1. 安装中文语言包
2. 从微软官网下载字体
3. 复制字体文件到 `C:\Windows\Fonts\` 目录

#### Linux 系统

安装中文字体包：

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei
sudo apt-get install fonts-noto-cjk

# CentOS/RHEL
sudo yum install wqy-microhei-fonts wqy-zenhei-fonts
sudo yum install google-noto-cjk-fonts

# 重新生成字体缓存
sudo fc-cache -fv
```

### 3. 自定义字体目录

您可以在项目中创建自定义字体目录：

```bash
# 创建字体目录
mkdir -p server/assets/fonts

# 设置环境变量
export FONT_DIR="./assets/fonts"
```

将字体文件复制到该目录：
```bash
cp /path/to/your/chinese-font.ttf server/assets/fonts/
```

### 4. 环境变量配置

在 `server/.env` 文件中设置字体目录：

```env
# 字体目录配置
FONT_DIR=./assets/fonts

# 输出目录配置
OUTPUT_DIR=./output
```

## 推荐字体

### 开源中文字体

1. **Noto Sans CJK** (Google)
   - 支持中日韩文字
   - 下载：https://github.com/googlefonts/noto-cjk

2. **Source Han Sans** (Adobe)
   - 思源黑体
   - 下载：https://github.com/adobe-fonts/source-han-sans

3. **文泉驿微米黑** (WenQuanYi Micro Hei)
   - 开源中文字体
   - 适用于Linux系统

### 系统字体

- **macOS**: PingFang SC, STHeiti
- **Windows**: 微软雅黑, 黑体, 宋体
- **Linux**: WenQuanYi Micro Hei, Noto Sans CJK

## 验证配置

### 1. 运行字体检查

```bash
npm run check-fonts
```

### 2. 测试中文文字

1. 在前端添加包含中文的文字元素
2. 导出视频
3. 检查生成的视频中文字是否正确显示

### 3. 查看日志

检查服务器日志中的字体相关信息：

```bash
tail -f logs/all.log | grep -i font
```

## 故障排除

### 问题1：找不到字体文件

**症状**：日志显示 "字体文件不存在" 警告

**解决方案**：
1. 检查字体目录是否存在
2. 验证字体文件权限
3. 确认字体文件格式正确（.ttf, .ttc, .otf）

### 问题2：中文仍显示乱码

**症状**：安装字体后中文仍显示为方块或乱码

**解决方案**：
1. 重启服务器应用
2. 清除字体缓存：`npm run setup-fonts`
3. 检查文本编码是否为UTF-8
4. 验证FFmpeg版本是否支持文本渲染

### 问题3：字体加载缓慢

**症状**：视频生成时间过长

**解决方案**：
1. 使用较小的字体文件
2. 将常用字体复制到本地目录
3. 启用字体缓存

## 技术实现

### FontManager 类

系统使用 `FontManager` 类来管理字体：

```typescript
// 自动检测文本类型并选择合适字体
const fontPath = fontManager.getBestFontPath(
  text,        // 文本内容
  fontFamily,  // 字体族
  styles,      // 样式（bold, italic）
  fontDir      // 自定义字体目录
);
```

### 字体选择逻辑

1. **检测文本类型**：识别中文、日文、韩文字符
2. **优先级排序**：自定义字体 > 系统字体 > 回退字体
3. **缓存机制**：避免重复的字体查找操作
4. **错误处理**：字体不存在时自动回退

### FFmpeg 集成

文本渲染使用 FFmpeg 的 `drawtext` 滤镜：

```bash
drawtext=text='中文文字':fontfile='/path/to/chinese-font.ttf':fontsize=24:fontcolor=0xffffff
```

## 更新日志

- **v1.0.0**: 初始版本，基本中文字体支持
- **v1.1.0**: 添加 FontManager 类和自动字体检测
- **v1.2.0**: 支持字体缓存和性能优化
- **v1.3.0**: 添加字体设置脚本和配置向导

## 相关链接

- [FFmpeg 文本渲染文档](https://ffmpeg.org/ffmpeg-filters.html#drawtext-1)
- [Unicode 中文字符范围](https://en.wikipedia.org/wiki/CJK_Unified_Ideographs)
- [字体文件格式说明](https://en.wikipedia.org/wiki/Font_file_format)
