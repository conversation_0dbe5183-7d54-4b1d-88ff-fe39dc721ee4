# 服务器配置
PORT=8080

# AWS Bedrock配置（用于AI助手功能）
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# AWS S3配置（用于媒体文件上传）
S3_BUCKET_NAME=your_s3_bucket_name
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your_s3_access_key_id
S3_SECRET_ACCESS_KEY=your_s3_secret_access_key
S3_SIGNED_URL_EXPIRES=900
S3_MAX_FILE_SIZE=104857600
S3_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,video/mp4,video/mov,video/avi,audio/mp3,audio/wav,audio/aac

# 其他配置
NODE_ENV=development
