{"name": "online-video-server", "version": "1.0.0", "description": "Server for online video editor", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest", "setup-fonts": "node scripts/setup-fonts.js", "check-fonts": "node scripts/setup-fonts.js", "test-font-styles": "npm run build && node scripts/test-font-styles.js", "init-templates": "ts-node-dev src/scripts/initializeTemplates.ts", "reset-templates": "ts-node-dev src/scripts/initializeTemplates.ts --reset"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.846.0", "@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/client-transcribe": "^3.846.0", "@aws-sdk/s3-request-presigner": "^3.848.0", "@types/imagemagick": "^0.0.35", "@types/node-fetch": "^2.6.12", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "imagemagick": "^0.1.3", "multer": "^2.0.1", "node-fetch": "^3.3.2", "sharp": "^0.34.3", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/multer": "^2.0.0", "@types/node": "^24.0.14", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "jest": "^29.7.0", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}