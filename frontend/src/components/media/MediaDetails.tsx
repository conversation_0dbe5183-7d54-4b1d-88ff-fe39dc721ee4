import React, { useState, useEffect } from "react";
import {
  Box,
  Card,
  CardMedia,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Chip,
  Divider,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tooltip,
} from "@mui/material";
import {
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  ContentCopy as CopyIcon,
} from "@mui/icons-material";
import {
  mediaLibraryService,
  MediaMetadata,
} from "../../services/s3UploadService";

export interface MediaDetailsProps {
  mediaId: string;
  open: boolean;
  onClose: () => void;
  onDelete?: (media: MediaMetadata) => void;
  onEdit?: (media: MediaMetadata) => void;
}

export const MediaDetails: React.FC<MediaDetailsProps> = ({
  mediaId,
  open,
  onClose,
  onDelete,
  onEdit,
}) => {
  const [media, setMedia] = useState<MediaMetadata | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  // 加载媒体详情
  useEffect(() => {
    if (open && mediaId) {
      loadMediaDetails();
    }
  }, [open, mediaId]);

  const loadMediaDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const mediaData = await mediaLibraryService.getMediaDetails(mediaId);
      setMedia(mediaData);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to load media details"
      );
    } finally {
      setLoading(false);
    }
  };

  // 处理删除
  const handleDelete = async () => {
    if (!media) return;

    try {
      const success = await mediaLibraryService.deleteMedia(media.id);
      if (success) {
        onDelete?.(media);
        onClose();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete media");
    }
    setDeleteConfirm(false);
  };

  // 复制URL到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 可以添加成功提示
    } catch (err) {
      console.error("Failed to copy to clipboard:", err);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    }
    return `${minutes}:${secs.toString().padStart(2, "0")}`;
  };

  // 获取文件类型显示名称
  const getFileTypeDisplay = (mimeType: string): string => {
    const typeMap: { [key: string]: string } = {
      "image/jpeg": "JPEG 图片",
      "image/png": "PNG 图片",
      "image/gif": "GIF 图片",
      "image/webp": "WebP 图片",
      "video/mp4": "MP4 视频",
      "video/mov": "MOV 视频",
      "video/avi": "AVI 视频",
      "audio/mp3": "MP3 音频",
      "audio/wav": "WAV 音频",
      "audio/aac": "AAC 音频",
    };
    return typeMap[mimeType] || mimeType;
  };

  if (!open) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: "60vh" },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6">媒体详情</Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : media ? (
          <Box
            sx={{
              display: "flex",
              gap: 3,
              flexDirection: { xs: "column", md: "row" },
            }}
          >
            {/* 媒体预览 */}
            <Box sx={{ flex: 1 }}>
              <Card>
                {media.fileType.startsWith("image/") ? (
                  <CardMedia
                    component="img"
                    image={media.url}
                    alt={media.fileName}
                    sx={{
                      maxHeight: 400,
                      objectFit: "contain",
                      backgroundColor: "grey.100",
                    }}
                  />
                ) : media.fileType.startsWith("video/") ? (
                  <CardMedia
                    component="video"
                    controls
                    src={media.url}
                    sx={{
                      maxHeight: 400,
                      width: "100%",
                    }}
                  />
                ) : media.fileType.startsWith("audio/") ? (
                  <Box sx={{ p: 3, textAlign: "center" }}>
                    <Typography variant="h1" sx={{ fontSize: "4rem", mb: 2 }}>
                      🎵
                    </Typography>
                    <audio controls style={{ width: "100%" }}>
                      <source src={media.url} type={media.fileType} />
                      您的浏览器不支持音频播放。
                    </audio>
                  </Box>
                ) : (
                  <Box
                    sx={{
                      height: 200,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "grey.100",
                      fontSize: "4rem",
                    }}
                  >
                    📄
                  </Box>
                )}
              </Card>
            </Box>

            {/* 媒体信息 */}
            <Box sx={{ flex: 1 }}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  {media.fileName}
                </Typography>

                <Chip
                  label={media.status}
                  size="small"
                  color={
                    media.status === "ready"
                      ? "success"
                      : media.status === "error"
                      ? "error"
                      : "default"
                  }
                  sx={{ mb: 2 }}
                />
              </Box>

              <Divider sx={{ mb: 2 }} />

              {/* 基本信息 */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="subtitle2"
                  color="text.secondary"
                  gutterBottom
                >
                  基本信息
                </Typography>

                <Box
                  sx={{
                    display: "grid",
                    gridTemplateColumns: "1fr 2fr",
                    gap: 1,
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    文件类型:
                  </Typography>
                  <Typography variant="body2">
                    {getFileTypeDisplay(media.fileType)}
                  </Typography>

                  <Typography variant="body2" color="text.secondary">
                    文件大小:
                  </Typography>
                  <Typography variant="body2">
                    {formatFileSize(media.fileSize)}
                  </Typography>

                  <Typography variant="body2" color="text.secondary">
                    上传时间:
                  </Typography>
                  <Typography variant="body2">
                    {new Date(media.uploadDate).toLocaleString()}
                  </Typography>

                  {media.width && media.height && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        分辨率:
                      </Typography>
                      <Typography variant="body2">
                        {media.width} × {media.height}
                      </Typography>
                    </>
                  )}

                  {media.duration && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        时长:
                      </Typography>
                      <Typography variant="body2">
                        {formatDuration(media.duration)}
                      </Typography>
                    </>
                  )}
                </Box>
              </Box>

              <Divider sx={{ mb: 2 }} />

              {/* URL信息 */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="subtitle2"
                  color="text.secondary"
                  gutterBottom
                >
                  访问链接
                </Typography>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      flex: 1,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      backgroundColor: "grey.100",
                      p: 1,
                      borderRadius: 1,
                    }}
                  >
                    {media.url}
                  </Typography>
                  <Tooltip title="复制链接">
                    <IconButton
                      size="small"
                      onClick={() => copyToClipboard(media.url)}
                    >
                      <CopyIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              {/* 操作按钮 */}
              <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                <Button
                  variant="contained"
                  startIcon={<DownloadIcon />}
                  href={media.url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  下载
                </Button>

                {onEdit && (
                  <Button
                    variant="outlined"
                    startIcon={<EditIcon />}
                    onClick={() => onEdit(media)}
                  >
                    编辑
                  </Button>
                )}

                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => setDeleteConfirm(true)}
                >
                  删除
                </Button>
              </Box>
            </Box>
          </Box>
        ) : null}
      </DialogContent>

      {/* 删除确认对话框 */}
      <Dialog open={deleteConfirm} onClose={() => setDeleteConfirm(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除文件 "{media?.fileName}" 吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirm(false)}>取消</Button>
          <Button color="error" onClick={handleDelete}>
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
};
