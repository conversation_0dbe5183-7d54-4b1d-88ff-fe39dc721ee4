import React, { useState, useEffect, useCallback, memo, useMemo } from "react";
import {
  Box,
  Typography,
  IconButton,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Pagination,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tooltip,
  ImageList,
  ImageListItem,
  InputAdornment,
  Skeleton,
  Stack,
  Tab,
  Tabs,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  CloudUpload as UploadIcon,
  PlayArrow as PlayIcon,
  VolumeUp as AudioIcon,
  Image as ImageIcon,
} from "@mui/icons-material";
import {
  mediaLibraryService,
  MediaMetadata,
} from "../../services/s3UploadService";
import { UnifiedFileUpload } from "../common/UnifiedFileUpload";

export interface MediaLibraryProps {
  onMediaSelect?: (media: MediaMetadata) => void;
  selectionMode?: boolean;
  allowedTypes?: string[];
  maxSelections?: number;
}

// 骨架屏组件
const MediaSkeleton = memo(() => (
  <ImageListItem>
    <Skeleton
      variant="rectangular"
      sx={{
        width: "100%",
        height: 220,
        borderRadius: 2,
      }}
      animation="wave"
    />
  </ImageListItem>
));

// 骨架屏列表
const SkeletonList = memo(({ cols }: { cols: number }) => (
  <ImageList variant="masonry" cols={cols} gap={16} sx={{ margin: 0 }}>
    {Array.from(new Array(8)).map((_, index) => (
      <MediaSkeleton key={`skeleton-${index}`} />
    ))}
  </ImageList>
));

// 媒体项组件
interface MediaItemProps {
  media: MediaMetadata;
  onMediaSelect: (media: MediaMetadata) => void;
  onDelete: (media: MediaMetadata) => void;
  isSelected: boolean;
  isDisabled: boolean;
  selectionMode: boolean;
}

const MediaItem = memo(
  ({
    media,
    onMediaSelect,
    onDelete,
    isSelected,
    isDisabled,
    selectionMode,
  }: MediaItemProps) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    // 格式化文件大小
    const formatFileSize = useCallback((bytes: number): string => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }, []);

    // 处理点击
    const handleClick = useCallback(() => {
      if (!isDisabled) {
        onMediaSelect(media);
      }
    }, [isDisabled, onMediaSelect, media]);

    // 处理删除
    const handleDelete = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        onDelete(media);
      },
      [onDelete, media]
    );

    // 处理下载
    const handleDownload = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        window.open(media.url, "_blank");
      },
      [media.url]
    );

    // 获取媒体预览内容
    const getMediaPreview = () => {
      if (media.fileType.startsWith("image/")) {
        return (
          <Box
            component="img"
            src={imageError ? undefined : media.thumbnailUrl || media.url}
            alt={media.fileName}
            onLoad={() => setIsLoaded(true)}
            onError={() => {
              setImageError(true);
              setIsLoaded(true);
            }}
            sx={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              borderRadius: 2,
              display: imageError ? "none" : "block",
            }}
          />
        );
      }

      // 非图片文件显示图标
      const getFileIcon = () => {
        if (media.fileType.startsWith("video/"))
          return <PlayIcon sx={{ fontSize: 40 }} />;
        if (media.fileType.startsWith("audio/"))
          return <AudioIcon sx={{ fontSize: 40 }} />;
        return <ImageIcon sx={{ fontSize: 40 }} />;
      };

      return (
        <Box
          sx={{
            width: "100%",
            height: 200,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "grey.100",
            borderRadius: 2,
            color: "grey.600",
          }}
        >
          {getFileIcon()}
          <Typography
            variant="caption"
            sx={{ mt: 1, px: 1, textAlign: "center" }}
          >
            {media.fileName}
          </Typography>
        </Box>
      );
    };

    return (
      <ImageListItem
        onClick={handleClick}
        sx={{
          cursor: isDisabled ? "not-allowed" : "pointer",
          position: "relative",
          transition: "all 0.3s ease",
          borderRadius: 2,
          overflow: "hidden",
          opacity: isDisabled ? 0.5 : isLoaded ? 1 : 0.7,
          border: isSelected ? 2 : 0,
          borderColor: "primary.main",
          "&:hover": {
            transform: isDisabled ? "none" : "translateY(-4px)",
            boxShadow: isDisabled ? 1 : 3,
          },
        }}
      >
        <Box sx={{ width: "100%", position: "relative" }}>
          {getMediaPreview()}

          {/* 媒体信息覆盖层 */}
          <Box
            sx={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
              background: "linear-gradient(transparent, rgba(0,0,0,0.8))",
              color: "white",
              p: 1,
            }}
          >
            <Tooltip title={media.fileName}>
              <Typography variant="caption" noWrap sx={{ fontWeight: 500 }}>
                {media.fileName}
              </Typography>
            </Tooltip>

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mt: 0.5,
              }}
            >
              <Typography variant="caption">
                {formatFileSize(media.fileSize)}
              </Typography>

              {media.duration && (
                <Typography variant="caption">
                  {Math.round(media.duration)}s
                </Typography>
              )}
            </Box>

            {media.width && media.height && (
              <Typography variant="caption" display="block">
                {media.width} × {media.height}
              </Typography>
            )}
          </Box>

          {/* 操作按钮 */}
          <Box
            sx={{
              position: "absolute",
              top: 8,
              right: 8,
              display: "flex",
              gap: 1,
              opacity: 0,
              transition: "opacity 0.2s",
              ".MuiImageListItem-root:hover &": {
                opacity: 1,
              },
            }}
          >
            <IconButton
              size="small"
              onClick={handleDownload}
              sx={{
                bgcolor: "rgba(255,255,255,0.9)",
                "&:hover": { bgcolor: "white" },
              }}
            >
              <DownloadIcon fontSize="small" />
            </IconButton>

            <IconButton
              size="small"
              color="error"
              onClick={handleDelete}
              sx={{
                bgcolor: "rgba(255,255,255,0.9)",
                "&:hover": { bgcolor: "white" },
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>

          {/* 状态指示器 */}
          <Chip
            label={media.status}
            size="small"
            color={
              media.status === "ready"
                ? "success"
                : media.status === "error"
                ? "error"
                : "default"
            }
            sx={{
              position: "absolute",
              top: 8,
              left: 8,
              fontSize: "0.7rem",
            }}
          />

          {/* 选择模式下的选择指示器 */}
          {selectionMode && isSelected && (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: "rgba(25, 118, 210, 0.1)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: 2,
              }}
            >
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: "50%",
                  bgcolor: "primary.main",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "white",
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                }}
              >
                ✓
              </Box>
            </Box>
          )}
        </Box>
      </ImageListItem>
    );
  }
);

export const MediaLibrary: React.FC<MediaLibraryProps> = ({
  onMediaSelect,
  selectionMode = false,
  allowedTypes,
  maxSelections = 1,
}) => {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only("xs"));
  const isSm = useMediaQuery(theme.breakpoints.only("sm"));
  const isMd = useMediaQuery(theme.breakpoints.only("md"));

  // 动态计算列数
  const cols = useMemo(() => {
    if (isXs) return 1;
    if (isSm) return 2;
    if (isMd) return 3;
    return 4;
  }, [isXs, isSm, isMd]);

  const [mediaList, setMediaList] = useState<MediaMetadata[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMedia, setSelectedMedia] = useState<MediaMetadata[]>([]);

  // 分页和过滤状态
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [fileTypeFilter, setFileTypeFilter] = useState("");
  const [sortBy, setSortBy] = useState("uploadDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // 对话框状态
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
    open: boolean;
    media: MediaMetadata | null;
  }>({ open: false, media: null });

  // 文件类型标签
  const fileTypeTags = useMemo(
    () => [
      { label: "全部", value: "" },
      { label: "图片", value: "image" },
      { label: "视频", value: "video" },
      { label: "音频", value: "audio" },
    ],
    []
  );

  // 加载媒体列表
  const loadMediaList = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await mediaLibraryService.getMediaList({
        page,
        limit: 20,
        search: searchQuery || undefined,
        fileType: fileTypeFilter || undefined,
        sortBy: sortBy as any,
        sortOrder,
      });

      setMediaList(response.items);
      setTotalPages(response.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : "加载媒体文件失败");
    } finally {
      setLoading(false);
    }
  }, [page, searchQuery, fileTypeFilter, sortBy, sortOrder]);

  // 初始加载
  useEffect(() => {
    loadMediaList();
  }, [loadMediaList]);

  // 处理媒体选择
  const handleMediaSelect = useCallback(
    (media: MediaMetadata) => {
      if (!selectionMode) {
        onMediaSelect?.(media);
        return;
      }

      if (
        allowedTypes &&
        !allowedTypes.some((type) => media.fileType.startsWith(type))
      ) {
        return;
      }

      setSelectedMedia((prev) => {
        const isSelected = prev.some((m) => m.id === media.id);

        if (isSelected) {
          return prev.filter((m) => m.id !== media.id);
        } else {
          if (prev.length >= maxSelections) {
            return [...prev.slice(1), media];
          }
          return [...prev, media];
        }
      });
    },
    [selectionMode, allowedTypes, maxSelections, onMediaSelect]
  );

  // 处理删除
  const handleDelete = useCallback(async (media: MediaMetadata) => {
    try {
      const success = await mediaLibraryService.deleteMedia(media.id);
      if (success) {
        setMediaList((prev) => prev.filter((m) => m.id !== media.id));
        setSelectedMedia((prev) => prev.filter((m) => m.id !== media.id));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "删除文件失败");
    }
    setDeleteConfirmDialog({ open: false, media: null });
  }, []);

  // 处理上传完成
  const handleUploadComplete = useCallback(() => {
    loadMediaList();
    setShowUploadDialog(false);
  }, [loadMediaList]);

  // 处理搜索
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
      setPage(1);
    },
    []
  );

  // 处理标签点击
  const handleTagClick = useCallback((tagValue: string) => {
    setFileTypeFilter(tagValue);
    setPage(1);
  }, []);

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "grey.100",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      {/* 标题栏 */}
      <Box
        sx={{
          height: 56,
          display: "flex",
          alignItems: "center",
          px: 3,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
          bgcolor: "background.paper",
          borderRadius: "8px 8px 0 0",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
          媒体库
        </Typography>

        <Box sx={{ ml: "auto" }}>
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => setShowUploadDialog(true)}
            size="small"
          >
            上传文件
          </Button>
        </Box>
      </Box>

      {/* 搜索和过滤 */}
      <Box sx={{ p: 2 }}>
        <TextField
          fullWidth
          size="small"
          placeholder="搜索媒体文件..."
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              "&:hover": {
                "& > fieldset": { borderColor: "primary.main" },
              },
            },
          }}
        />

        {/* 文件类型标签 */}
        <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
          {fileTypeTags.map((tag) => (
            <Chip
              key={tag.value}
              label={tag.label}
              onClick={() => handleTagClick(tag.value)}
              color={fileTypeFilter === tag.value ? "primary" : "default"}
              size="small"
            />
          ))}
        </Stack>

        {/* 排序控制 */}
        <Box sx={{ display: "flex", gap: 2, mt: 2, alignItems: "center" }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>排序</InputLabel>
            <Select
              value={`${sortBy}-${sortOrder}`}
              label="排序"
              onChange={(e) => {
                const [field, order] = e.target.value.split("-");
                setSortBy(field);
                setSortOrder(order as "asc" | "desc");
                setPage(1);
              }}
            >
              <MenuItem value="uploadDate-desc">最新上传</MenuItem>
              <MenuItem value="uploadDate-asc">最早上传</MenuItem>
              <MenuItem value="fileName-asc">文件名 A-Z</MenuItem>
              <MenuItem value="fileName-desc">文件名 Z-A</MenuItem>
              <MenuItem value="fileSize-desc">文件大小 大-小</MenuItem>
              <MenuItem value="fileSize-asc">文件大小 小-大</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* 选择状态显示 */}
      {selectionMode && selectedMedia.length > 0 && (
        <Alert severity="info" sx={{ mx: 2, mb: 2 }}>
          已选择 {selectedMedia.length} 个文件
          {maxSelections > 1 && ` (最多 ${maxSelections} 个)`}
        </Alert>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert
          severity="error"
          sx={{ mx: 2, mb: 2 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* 媒体列表 */}
      <Box
        sx={{
          flex: 1,
          overflow: "auto",
          px: 2,
          pb: 2,
          "&::-webkit-scrollbar": {
            width: "5px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            borderRadius: "5px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.3)",
            },
          },
        }}
      >
        {loading ? (
          <SkeletonList cols={cols} />
        ) : mediaList.length === 0 ? (
          <Box sx={{ textAlign: "center", py: 8 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              暂无媒体文件
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              点击上传按钮开始添加文件
            </Typography>
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={() => setShowUploadDialog(true)}
            >
              上传文件
            </Button>
          </Box>
        ) : (
          <>
            <ImageList
              variant="masonry"
              cols={cols}
              gap={16}
              sx={{ margin: 0 }}
            >
              {mediaList.map((media) => (
                <MediaItem
                  key={media.id}
                  media={media}
                  onMediaSelect={handleMediaSelect}
                  onDelete={(media) =>
                    setDeleteConfirmDialog({ open: true, media })
                  }
                  isSelected={selectedMedia.some((m) => m.id === media.id)}
                  isDisabled={
                    allowedTypes
                      ? !allowedTypes.some((type) =>
                          media.fileType.startsWith(type)
                        )
                      : false
                  }
                  selectionMode={selectionMode}
                />
              ))}
            </ImageList>

            {/* 分页 */}
            {totalPages > 1 && (
              <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            )}
          </>
        )}
      </Box>

      {/* 上传对话框 */}
      <Dialog
        open={showUploadDialog}
        onClose={() => setShowUploadDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>上传媒体文件</DialogTitle>
        <DialogContent>
          <UnifiedFileUpload
            mode="multiple"
            variant="dropzone"
            useS3Upload={true}
            validation={{
              maxSize: 100 * 1024 * 1024, // 100MB
              allowedTypes: [
                ".jpg",
                ".png",
                ".gif",
                ".mp4",
                ".mov",
                ".mp3",
                ".wav",
              ],
            }}
            onUploadComplete={handleUploadComplete}
            placeholder="拖拽文件到此处或点击选择文件"
            description="支持图片、视频、音频文件，单个文件最大100MB"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUploadDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteConfirmDialog.open}
        onClose={() => setDeleteConfirmDialog({ open: false, media: null })}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除文件 "{deleteConfirmDialog.media?.fileName}"
            吗？此操作无法撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteConfirmDialog({ open: false, media: null })}
          >
            取消
          </Button>
          <Button
            color="error"
            onClick={() =>
              deleteConfirmDialog.media &&
              handleDelete(deleteConfirmDialog.media)
            }
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
