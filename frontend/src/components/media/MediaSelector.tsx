import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON>alogContent,
  <PERSON>alog<PERSON>ctions,
  Button,
  Tabs,
  Tab,
  Box,
  Typography,
} from "@mui/material";
import { MediaLibrary } from "./MediaLibrary";
import { UnifiedFileUpload } from "../common/UnifiedFileUpload";
import { MediaMetadata } from "../../services/s3UploadService";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`media-tabpanel-${index}`}
      aria-labelledby={`media-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

export interface MediaSelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (media: MediaMetadata) => void;
  allowedTypes?: string[];
  title?: string;
  uploadEnabled?: boolean;
}

export const MediaSelector: React.FC<MediaSelectorProps> = ({
  open,
  onClose,
  onSelect,
  allowedTypes,
  title = "选择媒体文件",
  uploadEnabled = true,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedMedia, setSelectedMedia] = useState<MediaMetadata | null>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleMediaSelect = (media: MediaMetadata) => {
    setSelectedMedia(media);
  };

  const handleConfirmSelect = () => {
    if (selectedMedia) {
      onSelect(selectedMedia);
      onClose();
      setSelectedMedia(null);
    }
  };

  const handleUploadComplete = (fileId: string, result: MediaMetadata) => {
    // 上传完成后自动选择该文件
    setSelectedMedia(result);
    setTabValue(0); // 切换到媒体库标签
  };

  const handleClose = () => {
    onClose();
    setSelectedMedia(null);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: "80vh" }
      }}
    >
      <DialogTitle>{title}</DialogTitle>
      
      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="媒体库" />
            {uploadEnabled && <Tab label="上传文件" />}
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <MediaLibrary
            onMediaSelect={handleMediaSelect}
            selectionMode={true}
            allowedTypes={allowedTypes}
            maxSelections={1}
          />
        </TabPanel>

        {uploadEnabled && (
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                上传新文件
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                上传完成后，文件将自动添加到媒体库中
              </Typography>
              
              <UnifiedFileUpload
                mode="single"
                variant="dropzone"
                useS3Upload={true}
                validation={{
                  maxSize: 100 * 1024 * 1024, // 100MB
                  allowedTypes: allowedTypes?.map(type => {
                    // 将类型前缀转换为文件扩展名
                    if (type === "image") return [".jpg", ".jpeg", ".png", ".gif", ".webp"];
                    if (type === "video") return [".mp4", ".mov", ".avi"];
                    if (type === "audio") return [".mp3", ".wav", ".aac"];
                    return [];
                  }).flat() || [".jpg", ".png", ".gif", ".mp4", ".mov", ".mp3", ".wav"],
                }}
                onUploadComplete={handleUploadComplete}
                placeholder="拖拽文件到此处或点击选择文件"
                description={`支持${allowedTypes?.join("、") || "图片、视频、音频"}文件，单个文件最大100MB`}
              />
            </Box>
          </TabPanel>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          取消
        </Button>
        <Button
          variant="contained"
          onClick={handleConfirmSelect}
          disabled={!selectedMedia}
        >
          确认选择
        </Button>
      </DialogActions>
    </Dialog>
  );
};
