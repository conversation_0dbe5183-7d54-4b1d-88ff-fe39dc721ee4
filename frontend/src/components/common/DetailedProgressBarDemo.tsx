import React, { useState, useEffect } from "react";
import { Box, Button, Typography, Paper } from "@mui/material";
import { DetailedProgressBar } from "./DetailedProgressBar";
import { UploadProgress } from "../../services/s3UploadService";

/**
 * 演示组件，用于测试详细进度条的各种状态
 */
export const DetailedProgressBarDemo: React.FC = () => {
  const [progress, setProgress] = useState<UploadProgress>({
    loaded: 0,
    total: 100 * 1024 * 1024, // 100MB
    percentage: 0,
    speed: 0,
    remainingTime: 0,
    startTime: Date.now(),
    elapsedTime: 0,
  });

  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      setProgress(prev => {
        const now = Date.now();
        const elapsedTime = (now - (prev.startTime || now)) / 1000;
        
        // 模拟上传进度
        const newLoaded = Math.min(prev.loaded + Math.random() * 2 * 1024 * 1024, prev.total);
        const newPercentage = (newLoaded / prev.total) * 100;
        
        // 模拟速度计算
        const speed = newLoaded / elapsedTime;
        const remainingBytes = prev.total - newLoaded;
        const remainingTime = speed > 0 ? remainingBytes / speed : 0;

        const newProgress: UploadProgress = {
          loaded: newLoaded,
          total: prev.total,
          percentage: newPercentage,
          speed,
          remainingTime,
          startTime: prev.startTime,
          elapsedTime,
        };

        // 如果完成了，停止模拟
        if (newPercentage >= 100) {
          setIsRunning(false);
        }

        return newProgress;
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isRunning]);

  const startDemo = () => {
    setProgress({
      loaded: 0,
      total: 100 * 1024 * 1024,
      percentage: 0,
      speed: 0,
      remainingTime: 0,
      startTime: Date.now(),
      elapsedTime: 0,
    });
    setIsRunning(true);
  };

  const stopDemo = () => {
    setIsRunning(false);
  };

  const resetDemo = () => {
    setIsRunning(false);
    setProgress({
      loaded: 0,
      total: 100 * 1024 * 1024,
      percentage: 0,
      speed: 0,
      remainingTime: 0,
      startTime: Date.now(),
      elapsedTime: 0,
    });
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: "auto" }}>
      <Typography variant="h5" gutterBottom>
        详细进度条演示
      </Typography>
      
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          详细模式
        </Typography>
        <DetailedProgressBar
          progress={progress}
          fileName="demo-file.mp4"
          variant="detailed"
        />
      </Paper>

      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          紧凑模式
        </Typography>
        <DetailedProgressBar
          progress={progress}
          fileName="demo-file.mp4"
          variant="compact"
        />
      </Paper>

      <Box sx={{ display: "flex", gap: 1, mt: 2 }}>
        <Button
          variant="contained"
          onClick={startDemo}
          disabled={isRunning}
        >
          开始演示
        </Button>
        <Button
          variant="outlined"
          onClick={stopDemo}
          disabled={!isRunning}
        >
          暂停
        </Button>
        <Button
          variant="outlined"
          onClick={resetDemo}
        >
          重置
        </Button>
      </Box>

      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary">
          当前状态：
        </Typography>
        <Typography variant="caption" component="pre" sx={{ display: "block", mt: 1 }}>
          {JSON.stringify(progress, null, 2)}
        </Typography>
      </Box>
    </Box>
  );
};

export default DetailedProgressBarDemo;
