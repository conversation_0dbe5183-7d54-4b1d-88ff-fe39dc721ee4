import React from 'react';
import { Box, CircularProgress, Typography, useTheme } from '@mui/material';
import { observer } from 'mobx-react-lite';
import { StoreContext } from '../store';

/**
 * 加载状态覆盖层组件
 * 当Store中的isLoading为true时显示
 */
const LoadingOverlay: React.FC = observer(() => {
  const store = React.useContext(StoreContext);
  const theme = useTheme();
  
  if (!store.isLoading) {
    return null;
  }
  
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        zIndex: 9999,
        backdropFilter: 'blur(3px)',
      }}
    >
      <CircularProgress 
        size={60} 
        thickness={4} 
        sx={{ 
          color: theme.palette.primary.main,
          marginBottom: 2 
        }} 
      />
      <Typography 
        variant="h6" 
        sx={{ 
          color: 'white',
          textAlign: 'center',
          maxWidth: '80%',
          fontWeight: 'medium'
        }}
      >
        {store.loadingMessage || '加载中...'}
      </Typography>
    </Box>
  );
});

export default LoadingOverlay;
