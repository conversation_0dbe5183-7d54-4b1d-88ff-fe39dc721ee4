import React, { createContext, useContext } from "react";
import { Store } from "./Store";
import { HistoryManager } from "./HistoryManager";

export { HistoryManager } from "./HistoryManager";
export const StoreContext = createContext<Store>(null!);

export function StoreProvider(props: { children: React.ReactNode }) {
  const [store] = React.useState(() => new Store());
  return (
    <StoreContext.Provider value={store}>
      {props.children}
    </StoreContext.Provider>
  );
}

export function useStore(): Store {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error("useStore must be used within a StoreProvider");
  }
  return store;
}
