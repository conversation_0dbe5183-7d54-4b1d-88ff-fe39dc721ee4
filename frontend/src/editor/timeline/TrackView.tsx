"use client";
import { Box, useTheme } from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useContext,
  useRef,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { StoreContext } from "../../store";
import { EditorElement, Track, TimeGap } from "../../types";
import { TimeFrameView } from "./TimeLine";
import { useDroppable } from "@dnd-kit/core";
import { useTrackDnd, useGlobalElementDragState } from "./TrackDndContext";
import TrackContextMenu from "./menu/TrackContextMenu";
import { GapIndicator } from "./GapIndicator";
import { useImageDrag } from "./TimeLinePanel";

// 轨道类型对应的颜色
const TRACK_COLORS = {
  media: "#4caf50", // 使用原来的image颜色作为media颜色
  audio: "#3f51b5",
  text: "#ff9800",
  caption: "#9c27b0",
} as const;

// 轨道高度常量
const TRACK_HEIGHT = "38px";

interface TrackViewProps {
  track: Track;
  elements: EditorElement[];
  isDraggable?: boolean;
}

export const TrackView = observer(
  ({ track, elements, isDraggable }: TrackViewProps) => {
    const store = useContext(StoreContext);
    const theme = useTheme();
    const trackRef = useRef<HTMLDivElement>(null);
    const [isHighlighted, setIsHighlighted] = useState(false);
    const [isTypeMismatch, setIsTypeMismatch] = useState(false);
    const [contextMenu, setContextMenu] = useState<{
      mouseX: number;
      mouseY: number;
    } | null>(null);
    const [containerWidth, setContainerWidth] = useState<number | null>(null);

    const { overTrack, activeElement } = useTrackDnd();
    const isElementDragging = useGlobalElementDragState();
    const { dragTargetTrack, isDragOver: isImageDragOver } = useImageDrag();

    const { setNodeRef } = useDroppable({
      id: `track-${track.id}`,
      data: {
        type: "track",
        trackId: track.id,
        trackType: track.type,
      },
    });

    const handleTimeFrameChange = useCallback(
      (element: EditorElement, start: number, end: number) => {
        // 设置isDragEnd为true，确保更新maxDuration
        store.updateEditorElementTimeFrame(element, { start, end }, true);
        store.trackManager.fixTrackElementsOverlap(track.id);
      },
      [store, track.id]
    );

    // 处理右键菜单
    const handleContextMenu = useCallback((event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setContextMenu({
        mouseX: event.clientX,
        mouseY: event.clientY,
      });
    }, []);

    const handleCloseContextMenu = useCallback(() => {
      setContextMenu(null);
    }, []);

    // 检测轨道中的时间间隙（拖拽时暂停检测）
    const timeGaps = useMemo(() => {
      if (!elements?.length || isElementDragging) return [];
      return store.trackManager.detectTimeGaps(track.id);
    }, [elements, track.id, store.trackManager, isElementDragging]);

    // 处理删除间隙
    const handleDeleteGap = useCallback(
      (gap: TimeGap) => {
        store.trackManager.deleteTimeGap(gap);
      },
      [store.trackManager]
    );

    // 获取时间线容器宽度
    const getTimelineContainerWidth = useCallback(() => {
      const container = document.querySelector(".timeline-elements-container");
      return container ? container.clientWidth : 800;
    }, []);

    // 监听容器宽度变化
    useEffect(() => {
      const updateContainerWidth = () => {
        setContainerWidth(getTimelineContainerWidth());
      };

      updateContainerWidth();
      window.addEventListener("resize", updateContainerWidth);
      return () => window.removeEventListener("resize", updateContainerWidth);
    }, [getTimelineContainerWidth]);

    // 处理轨道高亮（包括元素拖拽和图片拖拽）
    useEffect(() => {
      const isElementDragHighlight = overTrack?.id === track.id;
      const isImageDragHighlight =
        isImageDragOver && dragTargetTrack === track.id;
      setIsHighlighted(isElementDragHighlight || isImageDragHighlight);
    }, [overTrack, track.id, isImageDragOver, dragTargetTrack]);

    // 检测元素类型与轨道类型是否匹配
    useEffect(() => {
      let shouldCheckMismatch = false;
      let elementType = "";

      // 检查元素拖拽的类型匹配
      if (overTrack?.id === track.id && activeElement) {
        shouldCheckMismatch = true;
        elementType = activeElement.type;
      }
      // 检查图片拖拽的类型匹配
      else if (isImageDragOver && dragTargetTrack === track.id) {
        shouldCheckMismatch = true;
        elementType = "image";
      }

      if (shouldCheckMismatch) {
        let isMatch = false;

        // 对于image、video、shape、gif类型的元素，它们可以放在media类型的轨道中
        if (
          ["image", "video", "shape", "gif"].includes(elementType) &&
          track.type === "media"
        ) {
          isMatch = true;
        } else if (elementType === "text" && track.type === "text") {
          isMatch = true;
        } else if (elementType === "audio" && track.type === "audio") {
          isMatch = true;
        } else {
          isMatch = false;
        }

        setIsTypeMismatch(!isMatch);
      } else {
        setIsTypeMismatch(false);
      }
    }, [
      overTrack,
      track.id,
      track.type,
      activeElement,
      isImageDragOver,
      dragTargetTrack,
    ]);

    const trackStyles = useMemo(
      () => ({
        position: "relative",
        width: "100%",
        bgcolor: "grey.100",
        height: TRACK_HEIGHT,
        my: 0.125, // 进一步减小上下边距（从2px减小到1px）
        borderRadius: 1,
        transition: "all 0.2s ease",
        border: isTypeMismatch
          ? "2px dashed rgba(244, 67, 54, 0.8)" // 类型不匹配时显示红色虚线边框
          : isHighlighted
          ? "2px dashed rgba(33, 150, 243, 0.6)"
          : "none",
        ...(elements?.length > 0 && {
          marginLeft: "10px",
          overflow: "visible",
        }),
      }),
      [theme.palette.mode, isTypeMismatch, isHighlighted, elements?.length]
    );

    const setRefs = useCallback(
      (node: HTMLDivElement | null) => {
        trackRef.current = node;
        if (node) setNodeRef(node);
      },
      [setNodeRef]
    );

    const trackClassName = useMemo(
      () =>
        `timeline-track ${isHighlighted ? "track-highlight" : ""} ${
          isTypeMismatch ? "track-type-mismatch" : ""
        }`,
      [isHighlighted, isTypeMismatch]
    );

    if (!elements?.length) {
      return (
        <>
          <Box
            ref={setRefs}
            className={trackClassName}
            data-track-id={track.id}
            sx={trackStyles}
            onContextMenu={handleContextMenu}
          />
          <TrackContextMenu
            track={track}
            contextMenu={contextMenu}
            handleClose={handleCloseContextMenu}
          />
        </>
      );
    }

    return (
      <>
        <Box
          ref={setRefs}
          className={trackClassName}
          data-track-id={track.id}
          sx={trackStyles}
          onContextMenu={handleContextMenu}
        >
          <Box
            sx={{
              position: "absolute",
              inset: 0,
              width: "100%",
              height: "100%",
              cursor: "context-menu",
            }}
          >
            {/* 渲染时间间隙指示器 */}
            {timeGaps.map((gap) => (
              <GapIndicator
                key={gap.id}
                gap={gap}
                containerWidth={containerWidth || getTimelineContainerWidth()}
                timelineDisplayDuration={store.timelineDisplayDuration}
                timelinePanOffsetX={store.timelinePan.offsetX}
                onDeleteGap={handleDeleteGap}
              />
            ))}

            {/* 渲染时间线元素 */}
            {elements.map((element) => (
              <TimeFrameView
                key={element.id}
                element={element}
                handleTimeFrameChange={handleTimeFrameChange}
                allElements={elements}
                isDraggable={isDraggable}
              />
            ))}
          </Box>
        </Box>
        <TrackContextMenu
          track={track}
          contextMenu={contextMenu}
          handleClose={handleCloseContextMenu}
        />
      </>
    );
  }
);
