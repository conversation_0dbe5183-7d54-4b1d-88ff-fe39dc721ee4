"use client";
import { Box, Tooltip, alpha } from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useMemo,
  useEffect,
  useRef,
  useCallback,
  useState,
  useContext,
} from "react";
import { StoreContext } from "../../store";
import "./styles.css";
import {
  formatTime,
  calculateTimelinePosition,
  getTimelineContainerWidth,
} from "../../utils/timeUtils";
import {
  TextFields,
  Image,
  VideoFile,
  AudioFile,
  ShapeLine,
  Gif,
} from "@mui/icons-material";
import {
  useVideoThumbnail,
  useAudioWaveform,
  useGifStaticThumbnail,
} from "../../hooks/timelineMediaHooks";
import { useElementDrag } from "./hooks/useElementDrag";
import { SnapIndicator } from "./SnapIndicator";
import { useDraggable } from "@dnd-kit/core";
import { useTrackDnd } from "./TrackDndContext";
import { FixedSizeList as List } from "react-window";
import AutoSizer from "react-virtualized-auto-sizer";
import TimelineElementContextMenu from "./menu/TimelineElementContextMenu";
import LoadingProgressBar from "./LoadingProgressBar";

// 常量和配置
// =================================

// 时间线高度常量
const TIMELINE_HEIGHT = "36px";
const ITEM_HEIGHT = 36;
const OVERSCAN_COUNT = 2; // 进一步减少过扫描数量以提升性能
const MIN_ELEMENT_WIDTH = 40;
const DEFAULT_HANDLE_WIDTH = 8;
const MIN_HANDLE_WIDTH = 2;

// 元素类型到图标/颜色的映射
const ELEMENT_COLORS = {
  text: "#ff9800",
  image: "#4caf50",
  video: "#f44336",
  audio: "#3f51b5",
  shape: "#673AB7",
  gif: "#e91e63", // 粉红色，区别于图片的绿色
  default: "#9e9e9e",
} as const;

// 形状类型到名称的映射
const SHAPE_TYPE_TO_NAME: Record<string, string> = {
  rect: "矩形",
  roundedRect: "圆角矩形",
  circle: "圆形",
  ellipse: "椭圆",
  triangle: "三角形",
  line: "直线",
  diamond: "菱形",
  pentagon: "五边形",
  hexagon: "六边形",
  octagon: "八边形",
  rightArrow: "右箭头",
  upArrow: "上箭头",
  cross: "叉号",
  downArrow: "下箭头",
  star: "星形",
  fourPointStar: "四角星",
  sixPointStar: "六角星",
  eightPointStar: "八角星",
  sunBurst: "太阳形",
  semicircle: "半圆",
  quarterCircle: "四分之一圆",
  ring: "环形",
  halfRing: "半环",
  plus: "加号",
  arch: "拱形",
  parallelogram: "平行四边形",
  polygon: "多边形",
  wave: "波浪形",
};

// 预创建的图标对象，避免重复创建
const ICONS = {
  text: (
    <TextFields
      sx={{
        fontSize: "18px",
        width: "18px",
        height: "18px",
        color: "#ffffff",
        filter: "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8))",
        flexShrink: 0,
      }}
    />
  ),
  image: (
    <Image
      sx={{
        fontSize: "18px",
        width: "18px",
        height: "18px",
        color: "#ffffff",
        filter: "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8))",
        flexShrink: 0,
      }}
    />
  ),
  gif: (
    <Gif
      sx={{
        fontSize: "18px",
        width: "18px",
        height: "18px",
        color: "#ffffff",
        filter: "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8))",
        flexShrink: 0,
      }}
    />
  ),
  video: (
    <VideoFile
      sx={{
        fontSize: "18px",
        width: "18px",
        height: "18px",
        color: "#ffffff",
        filter: "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8))",
        flexShrink: 0,
      }}
    />
  ),
  audio: (
    <AudioFile
      sx={{
        fontSize: "18px",
        width: "18px",
        height: "18px",
        color: "#ffffff",
        filter: "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8))",
        flexShrink: 0,
      }}
    />
  ),
  shape: (
    <ShapeLine
      sx={{
        fontSize: "18px",
        width: "18px",
        height: "18px",
        color: "#ffffff",
        filter: "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8))",
        flexShrink: 0,
      }}
    />
  ),
} as const;

// 静态样式对象，避免重复创建
const STATIC_STYLES = {
  boxBase: {
    display: "inline-flex" as const,
    alignItems: "center",
    maxWidth: "200px",
    minWidth: "auto",
    p: 0.3,
    bgcolor: "rgba(0, 0, 0, 0.4)",
    borderRadius: 1,
    overflow: "hidden",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    flexShrink: 0,
  },
  textBase: {
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap" as const,
    maxWidth: "180px",
    userSelect: "none" as const,
    minWidth: 0,
    color: "#ffffff",
    fontWeight: 500,
    fontSize: "12px",
    lineHeight: "16px",
    WebkitTextStroke: "0.5px rgba(0, 0, 0, 0.3)",
    WebkitFontSmoothing: "antialiased" as const,
    MozOsxFontSmoothing: "grayscale" as const,
    textRendering: "optimizeLegibility" as const,
    flexShrink: 0,
  },
  iconBox: {
    display: "flex",
    alignItems: "center",
    mr: 0.5,
    flexShrink: 0,
  },
} as const;

// 优化的元素内容获取函数，使用缓存避免重复创建对象
const elementContentCache = new Map<
  string,
  { icon: React.ReactNode; text: string }
>();

const getElementContent = (element: any) => {
  const cacheKey = `${element.type}-${element.name}-${
    element.properties?.text || ""
  }-${element.properties?.shapeType || ""}`;

  if (elementContentCache.has(cacheKey)) {
    return elementContentCache.get(cacheKey)!;
  }

  let content: { icon: React.ReactNode; text: string };

  switch (element.type) {
    case "text":
      content = {
        icon: ICONS.text,
        text: element.properties?.text ?? element.name,
      };
      break;
    case "image":
      content = {
        icon: ICONS.image,
        text: element.name,
      };
      break;
    case "gif":
      content = {
        icon: ICONS.gif,
        text: element.name,
      };
      break;
    case "video":
      content = {
        icon: ICONS.video,
        text: element.name,
      };
      break;
    case "audio":
      content = {
        icon: ICONS.audio,
        text: element.name,
      };
      break;
    case "shape":
      const shapeName = element.properties?.shapeType
        ? SHAPE_TYPE_TO_NAME[element.properties.shapeType] || element.name
        : element.name;
      content = {
        icon: ICONS.shape,
        text: shapeName,
      };
      break;
    default:
      content = { icon: null, text: element.name || "" };
  }

  // 限制缓存大小，避免内存泄漏
  if (elementContentCache.size > 100) {
    const firstKey = elementContentCache.keys().next().value;
    elementContentCache.delete(firstKey);
  }

  elementContentCache.set(cacheKey, content);
  return content;
};

// 组件类型定义
interface ElementContentProps {
  element: any;
}

interface DurationLabelProps {
  duration: string;
  isVisible: boolean;
}

interface TimeFrameViewProps {
  element: any;
  containerWidth?: number | null;
  handleTimeFrameChange?: (element: any, start: number, end: number) => void;
  allElements?: any[];
  isDraggable?: boolean;
}

// 全局 ResizeObserver 实例，避免重复创建
let globalResizeObserver: ResizeObserver | null = null;
const observedElements = new Map<
  Element,
  (entry: ResizeObserverEntry) => void
>();

const getGlobalResizeObserver = () => {
  if (!globalResizeObserver) {
    globalResizeObserver = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        const callback = observedElements.get(entry.target);
        if (callback) {
          callback(entry);
        }
      });
    });
  }
  return globalResizeObserver;
};

// 优化的元素内容组件
const ElementContent = React.memo(
  ({ element }: ElementContentProps) => {
    const [showText, setShowText] = useState(true);
    const boxRef = useRef<HTMLDivElement>(null);

    // 使用 useMemo 缓存内容，优化依赖数组
    const content = useMemo(
      () => getElementContent(element),
      [
        element.type,
        element.name,
        element.properties?.text,
        element.properties?.shapeType,
      ]
    );

    // 优化 ResizeObserver 回调，使用 useCallback 缓存
    const handleResize = useCallback((entry: ResizeObserverEntry) => {
      setShowText(entry.contentRect.width > 50);
    }, []);

    useEffect(() => {
      const element = boxRef.current;
      if (!element) return;

      const observer = getGlobalResizeObserver();
      observedElements.set(element, handleResize);
      observer.observe(element);

      return () => {
        observedElements.delete(element);
        observer.unobserve(element);
      };
    }, [handleResize]);

    return (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          height: "100%",
          position: "relative",
          overflow: "visible",
        }}
      >
        <Box ref={boxRef} sx={STATIC_STYLES.boxBase}>
          {content.icon && <Box sx={STATIC_STYLES.iconBox}>{content.icon}</Box>}
          {showText && (
            <span style={STATIC_STYLES.textBase}>{content.text}</span>
          )}
        </Box>
      </Box>
    );
  },
  (prevProps, nextProps) => {
    const prev = prevProps.element;
    const next = nextProps.element;
    return (
      prev.id === next.id &&
      prev.type === next.type &&
      prev.name === next.name &&
      prev.properties?.text === next.properties?.text &&
      prev.properties?.shapeType === next.properties?.shapeType
    );
  }
);

// 优化的持续时间标签组件
const DurationLabel = React.memo(
  ({ duration, isVisible }: DurationLabelProps) => {
    const [displayState, setDisplayState] = useState({
      showLabel: true,
      compactMode: false,
    });
    const labelRef = useRef<HTMLDivElement>(null);

    const handleResize = useCallback((entry: ResizeObserverEntry) => {
      const parentWidth = entry.contentRect.width;
      setDisplayState({
        showLabel: parentWidth >= 70,
        compactMode: parentWidth < 100,
      });
    }, []);

    useEffect(() => {
      const element = labelRef.current?.parentElement;
      if (!element) return;

      const observer = getGlobalResizeObserver();
      observedElements.set(element, handleResize);
      observer.observe(element);

      return () => {
        observedElements.delete(element);
        observer.unobserve(element);
      };
    }, [handleResize]);

    const labelStyle = useMemo(
      () => ({
        position: "absolute" as const,
        right: displayState.compactMode ? 2 : 4,
        top: "50%",
        transform: "translateY(-50%)",
        fontSize: displayState.compactMode ? "10px" : "11px",
        lineHeight: displayState.compactMode ? "12px" : "14px",
        fontWeight: 500,
        opacity: isVisible ? 1 : 0,
        transition: "opacity 0.2s",
        pointerEvents: "none" as const,
        userSelect: "none" as const,
        color: "#ffffff",
        WebkitTextStroke: "0.3px rgba(0, 0, 0, 0.3)",
        WebkitFontSmoothing: "antialiased",
        MozOsxFontSmoothing: "grayscale",
        backgroundColor: "rgba(0, 0, 0, 0.3)",
        padding: "1px 4px",
        borderRadius: "3px",
        backdropFilter: "blur(1px)",
        WebkitBackdropFilter: "blur(1px)",
        flexShrink: 0,
        whiteSpace: "nowrap" as const,
      }),
      [displayState.compactMode, isVisible]
    );

    if (!displayState.showLabel) return null;

    return (
      <div ref={labelRef} style={labelStyle}>
        {duration}
      </div>
    );
  }
);

// 优化的主时间帧视图组件
export const TimeFrameView = observer(
  ({
    element,
    containerWidth = null,
    handleTimeFrameChange,
    allElements = [],
    isDraggable = false,
  }: TimeFrameViewProps) => {
    const store = useContext(StoreContext);
    const isSelected = store.selectedElement?.id === element.id;
    const [isDragging, setIsDragging] = useState(false);
    const [isHovering, setIsHovering] = useState(false);
    const [elementWidth, setElementWidth] = useState(0);
    const [contextMenu, setContextMenu] = useState<{
      mouseX: number;
      mouseY: number;
    } | null>(null);

    const elementRef = useRef<HTMLDivElement>(null);
    const {
      videoThumbnail,
      isLoading: videoLoading,
      loadingProgress,
      hasError: videoError,
    } = useVideoThumbnail(element);
    const audioWaveform = useAudioWaveform(element);
    const {
      gifStaticThumbnail,
      isLoading: gifLoading,
      hasError: gifError,
    } = useGifStaticThumbnail(element);

    // 使用拖拽上下文
    const { activeElement } = useTrackDnd();
    const isBeingDragged = activeElement?.id === element.id;

    // 配置垂直拖拽功能
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      isDragging: isDndDragging,
    } = useDraggable({
      id: element.id,
      data: {
        type: "element",
        element: element,
      },
      disabled: !isDraggable,
    });

    // 使用元素拖拽钩子
    const {
      handleLeftHandleDrag,
      handleRightHandleDrag,
      handleCenterDrag,
      isSnappedStart,
      isSnappedEnd,
    } = useElementDrag({
      element,
      allElements,
      containerWidth: containerWidth || getTimelineContainerWidth(),
      store,
    });

    // 处理右键菜单
    const handleContextMenu = useCallback(
      (event: React.MouseEvent) => {
        event.preventDefault();
        event.stopPropagation();
        store.setSelectedElement(element);
        setContextMenu({
          mouseX: event.clientX,
          mouseY: event.clientY,
        });
      },
      [element, store]
    );

    const handleCloseContextMenu = useCallback(() => {
      setContextMenu(null);
    }, []);

    // 优化元素宽度监听，使用全局 ResizeObserver
    const handleElementResize = useCallback((entry: ResizeObserverEntry) => {
      setElementWidth(entry.contentRect.width);
    }, []);

    useEffect(() => {
      const element = elementRef.current;
      if (!element) return;

      const observer = getGlobalResizeObserver();
      observedElements.set(element, handleElementResize);
      observer.observe(element);

      return () => {
        observedElements.delete(element);
        observer.unobserve(element);
      };
    }, [handleElementResize]);

    // 计算缓存值 - 优化依赖
    const elementColor = useMemo(
      () =>
        ELEMENT_COLORS[element.type as keyof typeof ELEMENT_COLORS] ||
        ELEMENT_COLORS.default,
      [element.type]
    );

    const duration = useMemo(
      () => formatTime(element.timeFrame.end - element.timeFrame.start),
      [element.timeFrame.start, element.timeFrame.end]
    );

    const handleWidth = useMemo(
      () =>
        elementWidth < MIN_ELEMENT_WIDTH
          ? MIN_HANDLE_WIDTH
          : DEFAULT_HANDLE_WIDTH,
      [elementWidth]
    );

    // 位置样式计算 - 减少不必要的依赖
    const positionStyles = useMemo(() => {
      const currentContainerWidth =
        containerWidth || getTimelineContainerWidth();

      if (!currentContainerWidth) {
        return {
          width: "0%",
          left: "0%",
          transform: "translateZ(0)",
        };
      }

      const width =
        ((element.timeFrame.end - element.timeFrame.start) /
          store.timelineDisplayDuration) *
        100;
      const left = calculateTimelinePosition(
        element.timeFrame.start,
        store.timelineDisplayDuration,
        store.timelinePan.offsetX,
        currentContainerWidth
      );

      return {
        width: `${width}%`,
        left: `${left}%`,
        transform: "translateZ(0)",
        willChange: "transform",
      };
    }, [
      element.timeFrame.start,
      element.timeFrame.end,
      store.timelineDisplayDuration,
      store.timelinePan.offsetX,
      containerWidth,
    ]);

    // 控制把手样式 - 创建静态样式对象
    const handleStyles = useMemo(
      () => ({
        bgcolor: "white",
        border: 2,
        borderColor: elementColor,
        borderRadius: "1px",
        width: `${handleWidth}px`,
        height: "16px",
        cursor: "ew-resize",
        transition: "all 0.2s",
        opacity: isHovering || isSelected ? 1 : 0.7,
        "&:hover": {
          bgcolor: alpha(elementColor, 0.2),
        },
      }),
      [elementColor, isHovering, isSelected, handleWidth]
    );

    // 背景样式 - 优化条件判断
    const backgroundStyles = useMemo(() => {
      const baseStyles = {
        position: "relative" as const,
        "&::after": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "transparent",
          zIndex: 1,
        },
      };

      // 根据元素类型优化背景样式计算
      switch (element.type) {
        case "image":
          if (element.properties?.src) {
            return {
              ...baseStyles,
              backgroundImage: `url(${element.properties.src})`,
              backgroundSize: "30px 100%",
              backgroundRepeat: "repeat-x",
            };
          }
          break;
        case "gif":
          // 如果是占位符，显示加载动画背景
          if (element.properties?.isPlaceholder) {
            return {
              ...baseStyles,
              background: `linear-gradient(90deg,
                ${alpha(elementColor, 0.3)} 0%,
                ${alpha(elementColor, 0.5)} 50%,
                ${alpha(elementColor, 0.3)} 100%)`,
              backgroundSize: "200% 100%",
              animation: "shimmer 1.5s infinite",
              "@keyframes shimmer": {
                "0%": { backgroundPosition: "-200% 0" },
                "100%": { backgroundPosition: "200% 0" },
              },
            };
          } else if (gifStaticThumbnail) {
            return {
              ...baseStyles,
              backgroundImage: `url(${gifStaticThumbnail})`,
              backgroundSize: "30px 100%",
              backgroundRepeat: "repeat-x",
            };
          }
          break;
        case "video":
          if (videoThumbnail) {
            return {
              ...baseStyles,
              backgroundImage: `url(${videoThumbnail})`,
              backgroundSize: "30px 100%",
              backgroundRepeat: "repeat-x",
            };
          }
          break;
        case "audio":
          if (audioWaveform) {
            return {
              ...baseStyles,
              backgroundImage: `url(${audioWaveform})`,
              backgroundSize: "100% 100%",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              imageRendering: "high-quality",
            };
          }
          break;
      }

      return {
        bgcolor: isSelected ? elementColor : alpha(elementColor, 0.8),
        "&:hover": {
          bgcolor: elementColor,
        },
      };
    }, [
      element.type,
      element.properties?.src,
      videoThumbnail,
      audioWaveform,
      gifStaticThumbnail,
      isSelected,
      elementColor,
    ]);

    const handleDoubleClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        store.handleSeek(element.timeFrame.start);
      },
      [store, element.timeFrame.start]
    );

    const handleMouseEnter = useCallback(() => setIsHovering(true), []);
    const handleMouseLeave = useCallback(() => setIsHovering(false), []);

    // 样式对象 - 使用 useMemo 优化
    const centerBoxStyle = useMemo(
      () => ({
        height: "100%",
        width: "100%",
        minWidth: "0px",
        borderColor: alpha(elementColor, 0.8),
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        cursor: "move",
        pl: elementWidth < MIN_ELEMENT_WIDTH ? 0 : 1,
        pr: elementWidth < MIN_ELEMENT_WIDTH ? 0 : 1,
        borderRadius: "8px",
        overflow: "hidden",
        boxSizing: "border-box",
        ...backgroundStyles,
      }),
      [backgroundStyles, elementWidth, elementColor]
    );

    const containerStyle = useMemo(
      () => ({
        position: "absolute" as const,
        width: "auto",
        height: TIMELINE_HEIGHT,
        transition: "all 0.2s ease",
        bgcolor: "transparent",
        zIndex: isSelected ? 20 : isDragging ? 30 : 10,
        boxSizing: "border-box" as const,
        ...(isSelected && {
          border: "2px dashed rgba(33, 150, 243, 0.6)",
        }),
        ...positionStyles,
      }),
      [isSelected, isDragging, positionStyles]
    );

    const dragStyle = useMemo(
      () =>
        transform
          ? {
              transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
              zIndex: 1000,
              opacity: 0.8,
              border: "2px dashed rgba(33, 150, 243, 0.6)",
            }
          : undefined,
      [transform]
    );

    // 优化鼠标事件处理器，减少闭包创建
    const handleMouseDown = useCallback(
      (e: React.MouseEvent) => {
        const target = e.target as HTMLElement;
        const isLeftHandle =
          target.id === "left-handle" || target.closest("#left-handle");
        const isRightHandle =
          target.id === "right-handle" || target.closest("#right-handle");

        if (isLeftHandle || isRightHandle) return;

        const startX = e.clientX;
        const startY = e.clientY;
        let hasMoved = false;
        let isVerticalDrag = false;
        let dragStarted = false;

        const handleMouseMove = (moveEvent: MouseEvent) => {
          const deltaX = Math.abs(moveEvent.clientX - startX);
          const deltaY = Math.abs(moveEvent.clientY - startY);

          if (!hasMoved) {
            if (deltaY > deltaX && deltaY > 8) {
              isVerticalDrag = true;
              hasMoved = true;
              elementRef.current?.classList.add("vertical-dragging");
            } else if (deltaX > 5) {
              hasMoved = true;
              if (!dragStarted) {
                dragStarted = true;
                const reactMouseEvent = e as React.MouseEvent<HTMLDivElement>;
                handleCenterDrag(reactMouseEvent);
              }
            }
          }
        };

        const handleMouseUp = () => {
          if (isVerticalDrag) {
            elementRef.current?.classList.remove("vertical-dragging");
          }

          if (!hasMoved && !isVerticalDrag) {
            store.setSelectedElement(element);
          }

          document.removeEventListener("mousemove", handleMouseMove);
          document.removeEventListener("mouseup", handleMouseUp);
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      },
      [store, element, handleCenterDrag]
    );

    // 优化左右把手的事件处理器
    const handleLeftHandleMouseDown = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        e.stopPropagation();
        handleLeftHandleDrag(e);
      },
      [handleLeftHandleDrag]
    );

    const handleRightHandleMouseDown = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        e.stopPropagation();
        handleRightHandleDrag(e);
      },
      [handleRightHandleDrag]
    );

    const showControls = (isHovering || isSelected) && !isDragging;

    return (
      <>
        <Box
          id={`element-${element.id}`}
          ref={(node: HTMLDivElement | null) => {
            elementRef.current = node;
            if (isDraggable && node) {
              setNodeRef(node);
            }
          }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onContextMenu={handleContextMenu}
          className={`timeline-element-container ${
            isBeingDragged ? "element-dragging" : ""
          } ${isSelected ? "element-selected" : ""}`}
          sx={{
            ...containerStyle,
            ...(dragStyle || {}),
          }}
          onMouseDown={handleMouseDown}
          {...(isDraggable ? { ...attributes, ...listeners } : {})}
        >
          {/* 吸附指示器 */}
          <SnapIndicator position="left" isVisible={isSnappedStart} />
          <SnapIndicator position="right" isVisible={isSnappedEnd} />

          {/* 垂直拖拽指示器 */}
          {isDraggable && <Box className="vertical-drag-indicator" />}

          {/* 中间可拖拽区域 */}
          <Box
            id="center"
            onDoubleClick={handleDoubleClick}
            sx={centerBoxStyle}
          >
            <ElementContent element={element} />

            {/* 视频加载状态条 */}
            {element.type === "video" && (
              <LoadingProgressBar
                isLoading={videoLoading}
                progress={loadingProgress}
                hasError={videoError}
                elementType="视频"
              />
            )}

            {/* GIF加载状态条 */}
            {element.type === "gif" && (
              <LoadingProgressBar
                isLoading={gifLoading}
                progress={gifLoading ? 50 : 100}
                hasError={gifError}
                elementType="GIF"
              />
            )}

            <DurationLabel
              duration={duration}
              isVisible={isHovering || isSelected}
            />
          </Box>

          {/* 左侧时间控制把手 */}
          {showControls && (
            <Box
              id="left-handle"
              sx={{
                ...handleStyles,
                position: "absolute",
                top: "50%",
                left: elementWidth < MIN_ELEMENT_WIDTH ? -1 : 0,
                transform: "translate(0%, -50%)",
                zIndex: 30,
                borderColor: isSnappedStart ? "#ff9800" : "#2196f3",
                boxShadow: isSnappedStart
                  ? "0 0 8px rgba(255,152,0,0.5)"
                  : "none",
              }}
              onMouseDown={handleLeftHandleMouseDown}
            />
          )}

          {/* 右侧时间控制把手 */}
          {showControls && (
            <Box
              id="right-handle"
              sx={{
                ...handleStyles,
                position: "absolute",
                top: "50%",
                right: elementWidth < MIN_ELEMENT_WIDTH ? -1 : 0,
                transform: "translate(0%, -50%)",
                zIndex: 30,
                borderColor: isSnappedEnd ? "#ff9800" : "#2196f3",
                boxShadow: isSnappedEnd
                  ? "0 0 8px rgba(255,152,0,0.5)"
                  : "none",
              }}
              onMouseDown={handleRightHandleMouseDown}
            />
          )}
        </Box>

        {/* 右键菜单 */}
        <TimelineElementContextMenu
          element={element}
          contextMenu={contextMenu}
          handleClose={handleCloseContextMenu}
        />
      </>
    );
  }
);

// 优化的虚拟化列表项组件
const TimelineItem = React.memo(
  ({
    index,
    style,
    data,
  }: {
    index: number;
    style: React.CSSProperties;
    data: any;
  }) => {
    const {
      elements,
      containerWidth,
      handleTimeFrameChange,
      allElements,
      isDraggable,
      visibleTimeRange,
    } = data;
    const element = elements[index];

    // 检查元素是否在可见时间范围内
    const isVisible = useMemo(() => {
      if (!visibleTimeRange) return true;
      return (
        element.timeFrame.end >= visibleTimeRange.start &&
        element.timeFrame.start <= visibleTimeRange.end
      );
    }, [element.timeFrame, visibleTimeRange]);

    if (!isVisible) {
      return <div style={style} className="timeline-item-placeholder" />;
    }

    return (
      <div style={style} className="timeline-item timeline-track">
        <TimeFrameView
          element={element}
          containerWidth={containerWidth}
          handleTimeFrameChange={handleTimeFrameChange}
          allElements={allElements}
          isDraggable={isDraggable}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    if (prevProps.index !== nextProps.index) return false;

    const prevData = prevProps.data;
    const nextData = nextProps.data;
    const prevElement = prevData.elements[prevProps.index];
    const nextElement = nextData.elements[nextProps.index];

    if (prevElement.id !== nextElement.id) return false;

    const prevTimeFrame = prevElement.timeFrame;
    const nextTimeFrame = nextElement.timeFrame;

    if (
      prevTimeFrame.start !== nextTimeFrame.start ||
      prevTimeFrame.end !== nextTimeFrame.end
    ) {
      return false;
    }

    if (prevData.containerWidth !== nextData.containerWidth) return false;

    const prevRange = prevData.visibleTimeRange;
    const nextRange = nextData.visibleTimeRange;

    if (!prevRange !== !nextRange) return false;
    if (
      prevRange &&
      nextRange &&
      (prevRange.start !== nextRange.start || prevRange.end !== nextRange.end)
    ) {
      return false;
    }

    return true;
  }
);

// 优化排序函数，使用稳定排序
const sortElements = (elements: any[]) => {
  return elements.slice().sort((a, b) => {
    const timeDiff = a.timeFrame.start - b.timeFrame.start;
    return timeDiff !== 0 ? timeDiff : a.id.localeCompare(b.id);
  });
};

// 主时间线组件
export const TimeLine = observer(() => {
  const store = useContext(StoreContext);
  const containerRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  // 计算可见时间范围 - 优化依赖
  const visibleTimeRange = useMemo(() => {
    if (containerWidth === null) return null;

    const visibleStart = store.timelinePan.offsetX;
    const visibleEnd = visibleStart + store.timelineDisplayDuration;

    return { start: visibleStart, end: visibleEnd };
  }, [
    store.timelinePan.offsetX,
    store.timelineDisplayDuration,
    containerWidth,
  ]);

  // 获取排序后的元素 - 使用优化的排序函数
  const elements = useMemo(
    () => sortElements(store.editorElements),
    [store.editorElements]
  );

  // 处理时间帧变化 - 使用 useCallback 避免重复创建
  const handleTimeFrameChange = useCallback(
    (element: any, start: number, end: number) => {
      store.updateEditorElementTimeFrame(element, { start, end }, true);
    },
    [store]
  );

  // 列表项数据 - 优化依赖数组
  const itemData = useMemo(
    () => ({
      elements,
      containerWidth,
      handleTimeFrameChange,
      allElements: elements,
      isDraggable: true,
      visibleTimeRange,
    }),
    [elements, containerWidth, handleTimeFrameChange, visibleTimeRange]
  );

  // 优化容器宽度监听
  const handleContainerResize = useCallback(() => {
    const newWidth = getTimelineContainerWidth();
    setContainerWidth(newWidth);
  }, []);

  useEffect(() => {
    handleContainerResize();

    const observer = getGlobalResizeObserver();
    const container = document.querySelector(".timeline-container");

    if (container) {
      observedElements.set(container, handleContainerResize);
      observer.observe(container);

      return () => {
        observedElements.delete(container);
        observer.unobserve(container);
      };
    }
  }, [handleContainerResize]);

  return (
    <div
      ref={containerRef}
      style={{ width: "100%", height: "100%" }}
      className="timeline-elements-container"
    >
      <AutoSizer>
        {({ height, width }) => (
          <List
            ref={listRef}
            height={height}
            width={width}
            itemCount={elements.length}
            itemSize={ITEM_HEIGHT}
            itemData={itemData}
            overscanCount={OVERSCAN_COUNT}
            className="timeline-elements-list"
          >
            {TimelineItem}
          </List>
        )}
      </AutoSizer>
    </div>
  );
});
