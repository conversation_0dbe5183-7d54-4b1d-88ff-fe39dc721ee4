import {
  <PERSON>,
  Tab,
  Tabs,
  <PERSON><PERSON><PERSON>,
  Alert,
  CircularProgress,
  Divider,
  LinearProgress,
} from "@mui/material";
import { useState, useCallback } from "react";
import { UnifiedFileUpload } from "../../components/common";
import { MediaLibrary } from "../../components/media/MediaLibrary";
import { useLanguage } from "../../i18n/LanguageContext";
import {
  s3UploadService,
  MediaMetadata,
  UploadProgress,
} from "../../services/s3UploadService";
import { useStore } from "../../store";
import { getUid } from "../../utils";
import { DetailedProgressBar } from "../../components/common/DetailedProgressBar";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`upload-tabpanel-${index}`}
      aria-labelledby={`upload-tab-${index}`}
      {...other}
      sx={{ mt: 2 }}
    >
      {value === index && children}
    </Box>
  );
}

export const Uploads = () => {
  const [value, setValue] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<{
    isUploading: boolean;
    message: string;
    type: "success" | "error" | "info";
    progress?: UploadProgress;
    currentFile?: string;
    completedFiles?: number;
    totalFiles?: number;
  } | null>(null);
  const [refreshMediaLibrary, setRefreshMediaLibrary] = useState(0);
  const { t } = useLanguage();
  const store = useStore();

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  // 添加媒体到时间轴的函数
  const addMediaToTimeline = useCallback(
    async (media: MediaMetadata) => {
      try {
        const elementId = getUid();
        const currentTime = store.currentTimeInMs;

        // 根据媒体类型添加到时间轴
        if (media.fileType.startsWith("image/")) {
          // 添加图片
          const timeFrame = {
            start: currentTime,
            end: currentTime + 5000, // 默认5秒
          };

          const placeholderElement = store.addImagePlaceholder(
            elementId,
            media.url,
            timeFrame,
            {
              name: media.fileName,
              width: media.width,
              height: media.height,
            }
          );

          // 创建图片元素并加载
          const img = new Image();
          img.crossOrigin = "anonymous";
          img.onload = () => {
            store.replacePlaceholderWithImage(placeholderElement, img);
            console.log(`Image ${media.fileName} added to timeline`);
          };
          img.onerror = (error) => {
            console.error(`Failed to load image ${media.fileName}:`, error);
          };
          img.src = media.url;
        } else if (media.fileType.startsWith("video/")) {
          // 添加视频
          const videoDuration = media.duration ? media.duration * 1000 : 10000; // 默认10秒
          const timeFrame = {
            start: currentTime,
            end: currentTime + videoDuration,
          };

          const placeholderElement = store.addVideoPlaceholder(
            elementId,
            media.url,
            timeFrame,
            {
              name: media.fileName,
              width: media.width,
              height: media.height,
              duration: media.duration,
            }
          );

          // 创建视频元素并加载
          const video = document.createElement("video");
          video.crossOrigin = "anonymous";
          video.onloadedmetadata = () => {
            store.replacePlaceholderWithVideo(placeholderElement, video);
            console.log(`Video ${media.fileName} added to timeline`);
          };
          video.onerror = (error) => {
            console.error(`Failed to load video ${media.fileName}:`, error);
          };
          video.src = media.url;
        } else if (media.fileType.startsWith("audio/")) {
          // 添加音频
          const audioDuration = media.duration ? media.duration * 1000 : 30000; // 默认30秒
          const timeFrame = {
            start: currentTime,
            end: currentTime + audioDuration,
          };

          const placeholderElement = store.addAudioPlaceholder(
            elementId,
            media.url,
            timeFrame,
            {
              name: media.fileName,
              duration: media.duration,
            }
          );

          // 创建音频元素并加载
          const audio = new Audio();
          audio.crossOrigin = "anonymous";
          audio.onloadedmetadata = () => {
            store.replacePlaceholderWithAudio(placeholderElement, audio);
            console.log(`Audio ${media.fileName} added to timeline`);
          };
          audio.onerror = (error) => {
            console.error(`Failed to load audio ${media.fileName}:`, error);
          };
          audio.src = media.url;
        }

        // 显示成功消息
        setUploadStatus({
          isUploading: false,
          message: `${media.fileName} 已添加到时间轴`,
          type: "success",
        });

        // 3秒后清除状态
        setTimeout(() => setUploadStatus(null), 3000);
      } catch (error) {
        console.error("Failed to add media to timeline:", error);
        setUploadStatus({
          isUploading: false,
          message: `添加 ${media.fileName} 到时间轴失败`,
          type: "error",
        });
        setTimeout(() => setUploadStatus(null), 5000);
      }
    },
    [store]
  );

  const handleFilesSelected = useCallback(async (files: File[]) => {
    console.log("Files selected:", files);

    if (files.length === 0) return;

    setUploadStatus({
      isUploading: true,
      message: `正在上传 ${files.length} 个文件...`,
      type: "info",
      totalFiles: files.length,
      completedFiles: 0,
    });

    try {
      let completedCount = 0;
      const uploadPromises = files.map(async (file) => {
        return await s3UploadService.uploadFile(file, {
          onProgress: (progress) => {
            setUploadStatus((prev) =>
              prev
                ? {
                    ...prev,
                    progress,
                    currentFile: file.name,
                    message: `正在上传 ${file.name} (${completedCount + 1}/${
                      files.length
                    })`,
                  }
                : null
            );
          },
          onComplete: () => {
            completedCount++;
            setUploadStatus((prev) =>
              prev
                ? {
                    ...prev,
                    completedFiles: completedCount,
                    message:
                      completedCount === files.length
                        ? `成功上传 ${completedCount} 个文件`
                        : `已完成 ${completedCount}/${files.length} 个文件`,
                  }
                : null
            );
          },
        });
      });

      const results = await Promise.all(uploadPromises);

      setUploadStatus({
        isUploading: false,
        message: `成功上传 ${results.length} 个文件`,
        type: "success",
        totalFiles: files.length,
        completedFiles: results.length,
      });

      // 刷新媒体库
      setRefreshMediaLibrary((prev) => prev + 1);

      // 3秒后清除状态
      setTimeout(() => setUploadStatus(null), 3000);
    } catch (error) {
      console.error("Upload failed:", error);
      setUploadStatus({
        isUploading: false,
        message: `上传失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`,
        type: "error",
      });

      // 5秒后清除错误状态
      setTimeout(() => setUploadStatus(null), 5000);
    }
  }, []);

  return (
    <Box
      sx={{
        flex: 1,
        borderRadius: 1,
        display: "flex",
        flexDirection: "column",
        height: "100%",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1">{t("your_media")}</Typography>
        {uploadStatus?.isUploading && (
          <CircularProgress size={20} sx={{ ml: 2 }} />
        )}
      </Box>

      {/* 上传状态提示 */}
      {uploadStatus && (
        <Box sx={{ p: 1 }}>
          <Alert severity={uploadStatus.type} sx={{ fontSize: "0.875rem" }}>
            {uploadStatus.message}
          </Alert>

          {/* 详细进度条 */}
          {uploadStatus.isUploading && uploadStatus.progress && (
            <Box sx={{ mt: 1 }}>
              <DetailedProgressBar
                progress={uploadStatus.progress}
                fileName={uploadStatus.currentFile}
                variant="detailed"
              />
            </Box>
          )}

          {/* 文件计数进度 */}
          {uploadStatus.isUploading && uploadStatus.totalFiles && (
            <Box
              sx={{
                mt: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography variant="caption" color="text.secondary">
                文件进度: {uploadStatus.completedFiles || 0} /{" "}
                {uploadStatus.totalFiles}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={
                  ((uploadStatus.completedFiles || 0) /
                    uploadStatus.totalFiles) *
                  100
                }
                sx={{
                  width: "60%",
                  height: 4,
                  borderRadius: 2,
                  ml: 2,
                }}
              />
            </Box>
          )}
        </Box>
      )}

      <Box sx={{ p: 2, flexGrow: 1, display: "flex", flexDirection: "column" }}>
        <Box sx={{ borderRadius: 1 }}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={value}
              onChange={handleChange}
              variant="fullWidth"
              aria-label="upload tabs"
              sx={{
                "& .MuiTab-root": {
                  textTransform: "none",
                  fontWeight: 500,
                },
              }}
            >
              <Tab label="上传文件" />
              <Tab label="媒体库" />
            </Tabs>
          </Box>
          <TabPanel value={value} index={0}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                上传媒体文件
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                支持图片、视频、音频文件，最大100MB
              </Typography>
            </Box>

            <UnifiedFileUpload
              mode="multiple"
              variant="dropzone"
              useS3Upload={true}
              onFilesSelected={handleFilesSelected}
              validation={{
                maxSize: 100 * 1024 * 1024, // 100MB
                allowedTypes: [
                  ".mp4",
                  ".mov",
                  ".avi",
                  ".mp3",
                  ".wav",
                  ".aac",
                  ".jpg",
                  ".jpeg",
                  ".png",
                  ".gif",
                  ".webp",
                ],
                maxCount: 10,
              }}
              placeholder="拖拽文件到此处或点击选择文件"
              description="支持批量上传，最多10个文件"
              disabled={uploadStatus?.isUploading}
            />

            <Divider sx={{ my: 2 }} />

            <Typography variant="body2" color="text.secondary">
              💡
              提示：上传的文件将自动保存到您的媒体库中，可以直接拖拽到时间轴使用
            </Typography>
          </TabPanel>

          <TabPanel value={value} index={1}>
            <Box sx={{ height: "400px", overflow: "auto" }}>
              <MediaLibrary
                key={refreshMediaLibrary}
                onMediaSelect={(media) => {
                  console.log("Selected media:", media);
                  addMediaToTimeline(media);
                }}
                selectionMode={true}
                maxSelections={1}
              />
            </Box>
          </TabPanel>
        </Box>
      </Box>
    </Box>
  );
};
